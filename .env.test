# Test Environment Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=smart_home_test_db

# JWT Configuration
JWT_SECRET=test_jwt_secret_key_for_testing_only
JWT_EXPIRY=24h

# MQTT Configuration (for testing)
MQTT_BROKER=localhost
MQTT_PORT=1883
MQTT_CLIENT_ID=smart_home_test_client
MQTT_USERNAME=test_user
MQTT_PASSWORD=test_password

# Email Configuration (for testing)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=test_password

# Server Configuration
SERVER_PORT=8081
GIN_MODE=test

# Test specific configurations
TEST_MODE=true
LOG_LEVEL=error
