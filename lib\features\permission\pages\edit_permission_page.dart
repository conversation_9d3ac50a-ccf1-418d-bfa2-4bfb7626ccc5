import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../view_models/permission_view_model.dart';
import '../../../core/models/home_model.dart';
import '../../../core/models/permission_model.dart';

class EditPermissionPage extends StatefulWidget {
  final Home home;
  final Permission permission;

  const EditPermissionPage({
    super.key,
    required this.home,
    required this.permission,
  });

  @override
  State<EditPermissionPage> createState() => _EditPermissionPageState();
}

class _EditPermissionPageState extends State<EditPermissionPage> {
  late bool _canView;
  late bool _canControl;
  late bool _canConfigure;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _canView = widget.permission.canView;
    _canControl = widget.permission.canControl;
    _canConfigure = widget.permission.canConfigure;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Sử<PERSON> quyền'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Consumer<PermissionViewModel>(
        builder: (context, viewModel, child) {
          return Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Resource Info
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.permission.resourceType == 'device' ? 'Thiết bị' : 'Khu vực',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                      Text(
                        _getResourceName(viewModel),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Người dùng',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                      Text(
                        _getUserName(viewModel),
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 16),

                // Permissions
                CheckboxListTile(
                  title: const Text('Xem'),
                  value: _canView,
                  onChanged: (value) {
                    setState(() {
                      _canView = value ?? false;
                      if (!_canView) {
                        _canControl = false;
                        _canConfigure = false;
                      }
                    });
                  },
                ),
                CheckboxListTile(
                  title: const Text('Điều khiển'),
                  value: _canControl,
                  onChanged: _canView ? (value) {
                    setState(() {
                      _canControl = value ?? false;
                      if (!_canControl) {
                        _canConfigure = false;
                      }
                    });
                  } : null,
                ),
                CheckboxListTile(
                  title: const Text('Cấu hình'),
                  value: _canConfigure,
                  onChanged: _canControl ? (value) {
                    setState(() {
                      _canConfigure = value ?? false;
                    });
                  } : null,
                ),

                const Spacer(),

                // Action Buttons
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: _isLoading ? null : () {
                          Navigator.of(context).pop();
                        },
                        child: const Text('Hủy'),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _updatePermission,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                        ),
                        child: _isLoading
                            ? const CircularProgressIndicator(color: Colors.white)
                            : const Text('Cập nhật'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  String _getResourceName(PermissionViewModel viewModel) {
    if (widget.permission.resourceType == 'device') {
      try {
        final device = viewModel.devices.firstWhere(
          (d) => d.id == widget.permission.resourceId,
        );
        return device.name;
      } catch (e) {
        return 'Unknown Device';
      }
    } else {
      try {
        final area = viewModel.areas.firstWhere(
          (a) => a.id == widget.permission.resourceId,
        );
        return area.displayName;
      } catch (e) {
        return 'Unknown Area';
      }
    }
  }

  String _getUserName(PermissionViewModel viewModel) {
    try {
      final user = viewModel.users.firstWhere(
        (u) => u.userId == widget.permission.userId,
      );
      return user.email;
    } catch (e) {
      return 'Unknown User';
    }
  }

  Future<void> _updatePermission() async {
    setState(() {
      _isLoading = true;
    });

    final request = PermissionRequest(
      userId: widget.permission.userId,
      deviceId: widget.permission.deviceId,
      areaId: widget.permission.areaId,
      canView: _canView,
      canControl: _canControl,
      canConfigure: _canConfigure,
    );

    final success = await context.read<PermissionViewModel>().updatePermission(
      homeId: widget.home.homeId,
      permissionId: widget.permission.permissionId,
      request: request,
    );

    setState(() {
      _isLoading = false;
    });

    if (mounted) {
      if (success) {
        Navigator.of(context).pop(true); // Return true to indicate success
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Cập nhật thành công'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Cập nhật thất bại'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}