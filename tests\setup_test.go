package tests

import (
	"database/sql"
	"fmt"
	"log"
	"os"
	"testing"

	"dh52110724-api-quan-ly-nha-thong-minh/config"
	"dh52110724-api-quan-ly-nha-thong-minh/database"
	"dh52110724-api-quan-ly-nha-thong-minh/models"

	"github.com/joho/godotenv"
	"gorm.io/gorm"
)

var TestDB *gorm.DB

// SetupTestDB khởi tạo database cho testing
func SetupTestDB() *gorm.DB {
	// Load test environment variables
	if err := godotenv.Load("../.env.test"); err != nil {
		log.Printf("Warning: .env.test file not found, using default test config")
	}

	// Override database name for testing
	os.Setenv("DB_NAME", "smart_home_test_db")
	
	cfg := config.LoadConfig()
	
	// Create test database if not exists
	createTestDatabase(cfg)
	
	// Connect to test database
	if err := database.Connect(); err != nil {
		log.Fatalf("Failed to connect to test database: %v", err)
	}
	
	TestDB = database.DB
	
	// Auto migrate all models
	if err := migrateTestDB(); err != nil {
		log.Fatalf("Failed to migrate test database: %v", err)
	}
	
	return TestDB
}

// createTestDatabase tạo test database nếu chưa tồn tại
func createTestDatabase(cfg *config.Config) {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/", 
		cfg.DBUser, cfg.DBPassword, cfg.DBHost, cfg.DBPort)
	
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		log.Fatalf("Failed to connect to MySQL: %v", err)
	}
	defer db.Close()
	
	// Create test database
	_, err = db.Exec("CREATE DATABASE IF NOT EXISTS smart_home_test_db")
	if err != nil {
		log.Fatalf("Failed to create test database: %v", err)
	}
}

// migrateTestDB thực hiện migration cho test database
func migrateTestDB() error {
	return TestDB.AutoMigrate(
		&models.User{},
		&models.Home{},
		&models.Area{},
		&models.Device{},
		&models.DeviceType{},
		&models.DeviceNetworkInfo{},
		&models.DeviceCommand{},
		&models.Permission{},
		&models.Role{},
		&models.HomeUser{},
		&models.AutomationRule{},
		&models.AutomationExecution{},
		&models.Invitation{},
	)
}

// CleanupTestDB xóa tất cả dữ liệu test
func CleanupTestDB() {
	if TestDB == nil {
		return
	}
	
	// Disable foreign key checks
	TestDB.Exec("SET FOREIGN_KEY_CHECKS = 0")
	
	// Truncate all tables
	tables := []string{
		"automation_executions",
		"automation_rules", 
		"device_commands",
		"permissions",
		"devices",
		"device_network_info",
		"areas",
		"home_users",
		"homes",
		"invitations",
		"users",
	}
	
	for _, table := range tables {
		TestDB.Exec(fmt.Sprintf("TRUNCATE TABLE %s", table))
	}
	
	// Re-enable foreign key checks
	TestDB.Exec("SET FOREIGN_KEY_CHECKS = 1")
}

// TestMain setup và cleanup cho tất cả tests
func TestMain(m *testing.M) {
	// Setup
	SetupTestDB()
	
	// Run tests
	code := m.Run()
	
	// Cleanup
	CleanupTestDB()
	
	os.Exit(code)
}
