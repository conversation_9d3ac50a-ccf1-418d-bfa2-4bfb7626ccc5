package router

import (
	"dh52110724-api-quan-ly-nha-thong-minh/config"
	"dh52110724-api-quan-ly-nha-thong-minh/controller"
	"dh52110724-api-quan-ly-nha-thong-minh/middleware"
	"dh52110724-api-quan-ly-nha-thong-minh/repository/permission"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

func SetupRouter(cfg *config.Config, db *gorm.DB, userController *controller.Authcontrollers, permissionController *controller.PermissionController, homeController *controller.HomeController, deviceController *controller.DeviceController, deviceTypeController *controller.DeviceTypeController, automationController *controller.AutomationController, invitationController *controller.InvitationController) *gin.Engine {
	r := gin.Default()

	permRepo := permission.NewPostgresRepository(db)

	// Khởi tạo middleware
	authMiddleware := middleware.NewAuthMiddleware(cfg)
	permMiddleware := middleware.NewPermissionMiddleware(cfg, permRepo)

	// Auth routes (không cần token)
	auth := r.Group("/api/auth")
	{
		auth.POST("/register", userController.Register)
		auth.POST("/verify-email", userController.VerifyEmailRegistration)      // Xác thực email sau đăng ký
		auth.POST("/resend-verification", userController.ResendVerificationOTP) // Gửi lại OTP xác thực email
		auth.POST("/login", userController.Login)
		auth.POST("/resend-otp", userController.ResendOTP)
		auth.POST("/verify-otp", userController.VerifyOTP)
	}

	// Password Management Routes - USE CASE 1: Quên mật khẩu (không cần token)
	passwordReset := r.Group("/api/password")
	{
		passwordReset.POST("/forgot/send-otp", userController.ForgotPassword) // Bước 1: Gửi OTP
		passwordReset.POST("/forgot/reset", userController.ResetPassword)     // Bước 2: Đặt lại mật khẩu với OTP
	}

	// Password Management Routes - USE CASE 2: Đổi mật khẩu (cần token)
	passwordChange := r.Group("/api/password")
	passwordChange.Use(authMiddleware.AuthMiddleware())
	{
		passwordChange.PUT("/change-password", userController.ChangePassword) // Đổi mật khẩu khi đã đăng nhập
	}

	// Invitation routes (không cần token)
	invitations := r.Group("/api/invitations")
	{
		invitations.GET("/accept/:token", invitationController.AcceptInvitation)   // Accept invitation via token
		invitations.GET("/page/:token", invitationController.AcceptInvitationPage) // Web page for accepting invitation
	}

	// Protected routes (cần token)
	protected := r.Group("/api/user")
	protected.Use(authMiddleware.AuthMiddleware())
	{
		protected.GET("/profile", userController.GetProfile)
		protected.PUT("/profile", userController.UpdateProfile)
	}

	admin := r.Group("/api/admin/homes")
	admin.Use(authMiddleware.AuthMiddleware())
	{
		// Home creation and listing (no specific home context)
		admin.POST("", homeController.CreateHome)
		admin.GET("", homeController.GetUserHomes)

		homeGroup := admin.Group("/:home_id")
		homeGroup.Use(permMiddleware.RequireMember()) // Must be member of home
		{
			// Basic home info (any member can view)
			homeGroup.GET("", homeController.GetHome)
			homeGroup.GET("/detail", homeController.GetHomeDetailWithStats) // Chi tiết nhà với thống kê
			homeGroup.GET("/areas", homeController.GetHomeAreas)
			homeGroup.GET("/devices", deviceController.GetDevicesInHome)
			homeGroup.GET("/users", homeController.GetHomeUsersWithRoles) // New endpoint with roles

			// ADMIN or OWNER only operations
			adminOnlyGroup := homeGroup.Group("")
			adminOnlyGroup.Use(permMiddleware.RequireAdminOrOwner())
			{
				adminOnlyGroup.PUT("", homeController.UpdateHome)
				adminOnlyGroup.POST("/users", homeController.AddUserToHome) // Thêm user trực tiếp
				adminOnlyGroup.POST("/users/promote", homeController.PromoteUser)

				// Area management
				adminOnlyGroup.POST("/area", homeController.CreateArea)
				adminOnlyGroup.PUT("/area/:area_id", homeController.UpdateArea)
				adminOnlyGroup.DELETE("/area/:area_id", homeController.DeleteArea)

				// Device-Area management
				adminOnlyGroup.PUT("/areas/:area_id/devices/:device_id", deviceController.AddDeviceToArea)
				adminOnlyGroup.DELETE("/areas/:area_id/devices/:device_id", deviceController.RemoveDeviceFromArea)

				// Device management in home
				adminOnlyGroup.POST("/devices", deviceController.AddDeviceToHome)                   // Thêm device vào home (POST /api/admin/homes/{home_id}/devices)
				adminOnlyGroup.DELETE("/devices/:device_id", deviceController.RemoveDeviceFromHome) // Xóa hoàn toàn device khỏi hệ thống

				// Permission routes - ADMIN/OWNER only (MEMBER không có quyền)
				adminOnlyGroup.GET("/permissions", permissionController.GetAllPermissions)
				adminOnlyGroup.GET("/permissions/devices", permissionController.GetDevicePermissions)
				adminOnlyGroup.GET("/permissions/areas", permissionController.GetAreaPermissions)
				adminOnlyGroup.POST("/permissions/area", permissionController.AddAreaPermission)
				adminOnlyGroup.POST("/permissions/device", permissionController.AddDevicePermission)
				adminOnlyGroup.PUT("/permissions/:permission_id", permissionController.UpdatePermission)
				adminOnlyGroup.DELETE("/permissions/:permission_id", permissionController.DeletePermission)
			}

			// OWNER only operations
			ownerOnlyGroup := homeGroup.Group("")
			ownerOnlyGroup.Use(permMiddleware.RequireOwner())
			{
				ownerOnlyGroup.DELETE("", homeController.DeleteHome)
				ownerOnlyGroup.POST("/transfer-ownership", homeController.TransferOwnership)
				// Invitation management - OWNER only
				ownerOnlyGroup.POST("/invitations", homeController.SendInvitation)
				ownerOnlyGroup.GET("/invitations", homeController.GetHomeInvitations)
				ownerOnlyGroup.POST("/invitations/:invitation_id/resend", homeController.ResendInvitation)
			}

			// User removal with kick logic (handled by service)
			homeGroup.DELETE("/users/:user_id", homeController.RemoveUserFromHome)

			// Read-only area info
			homeGroup.GET("/area/:area_id", homeController.GetArea)
			homeGroup.GET("/areas/:area_id/devices", deviceController.GetDevicesInArea)
		}
	}

	// Member routes
	member := r.Group("/api/member")
	member.Use(authMiddleware.AuthMiddleware(), permMiddleware.RequireMember())
	{
		member.GET("/devices", permissionController.ListDevices)
		member.PUT("/devices/:id", permissionController.UpdateDevice)
	}

	// Device Management Routes - Tách biệt rõ ràng
	deviceAPI := r.Group("/api")

	// Public test endpoints (no auth required)
	deviceAPI.POST("/test-mqtt", deviceController.TestMQTT)                                                 // Test MQTT connection
	deviceAPI.POST("/homes/:home_id/devices/:device_id/test-control", deviceController.TestFullControlFlow) // Test device control flow
	deviceAPI.GET("/debug/devices/:device_id", deviceController.DebugDeviceInfo)                            // Debug device info
	deviceAPI.GET("/mqtt/heartbeat", deviceController.GetMQTTHeartbeatStatus)                               // MQTT heartbeat status
	deviceAPI.POST("/mqtt/check-timeout", deviceController.ForceCheckDevicesTimeout)                        // Force check devices timeout

	deviceAPI.Use(authMiddleware.AuthMiddleware())
	{

		// Global device management
		devices := deviceAPI.Group("/devices")
		{
			devices.GET("", deviceController.GetDevices)                 // Tất cả devices trong hệ thống
			devices.GET("/:device_id", deviceController.GetDevice)       // Chi tiết 1 device
			devices.PUT("/:device_id", deviceController.UpdateDevice)    // Update device info
			devices.DELETE("/:device_id", deviceController.DeleteDevice) // Xóa device khỏi hệ thống
		}

		homeGroup := deviceAPI.Group("/homes/:home_id")
		homeGroup.Use(permMiddleware.RequireMember()) // Must be member of home
		{
			// Device management trong home cụ thể
			homeDevices := homeGroup.Group("/devices")
			{
				// Basic device operations (any member can view)
				homeDevices.GET("", deviceController.GetDevicesInHome) // Devices thuộc home này
				homeDevices.GET("/status", deviceController.GetAllRealTimeDeviceStatus)
				homeDevices.GET("/:device_id/status", deviceController.GetRealTimeDeviceStatus)
				homeDevices.GET("/types", deviceController.GetDeviceTypes)                     // Device types có trong home
				homeDevices.GET("/by-type/:device_type_id", deviceController.GetDevicesByType) // Filter devices by type
				homeDevices.GET("/:device_id/properties", deviceController.GetDeviceProperties)
				homeDevices.GET("/:device_id/commands", deviceController.GetDeviceCommands)
				homeDevices.GET("/:device_id/history", deviceController.GetDeviceHistory)

				// Control operations (any member can control devices they have permission for)
				homeDevices.PUT("/:device_id/control", deviceController.ControlDevice)
				homeDevices.PUT("/:device_id/property/:property", deviceController.UpdateDeviceProperty)
				homeDevices.POST("/batch-control", deviceController.BatchControlDevices)
				homeDevices.POST("/:device_id/monitor", deviceController.StartDeviceMonitoring)

				// ADMIN/OWNER only operations
				adminDeviceGroup := homeDevices.Group("")
				adminDeviceGroup.Use(permMiddleware.RequireAdminOrOwner())
				{
					// Note: Device creation is handled by /api/admin/homes/{home_id}/devices endpoint above
					adminDeviceGroup.DELETE("/:device_id", deviceController.RemoveDeviceFromHome) // Xóa hoàn toàn device khỏi hệ thống
				}
			}
		}
		// Automation & Rule Engine routes
		automation := r.Group("/api/automation")
		automation.Use(authMiddleware.AuthMiddleware())
		{
			// Rule management (implemented)
			automation.POST("/rules/schedule", automationController.CreateScheduleRule)
			automation.POST("/rules/condition", automationController.CreateConditionRule)
			automation.GET("/rules", automationController.GetUserRules)
			automation.GET("/rules/:rule_id", automationController.GetRule)
			automation.PUT("/rules/:rule_id", automationController.UpdateRule)
			automation.DELETE("/rules/:rule_id", automationController.DeleteRule)
			automation.PUT("/rules/:rule_id/status", automationController.ToggleRuleStatus)

			//Rule execution (to be implemented)
			automation.POST("/rules/:rule_id/execute", automationController.ExecuteHomeAutomationRule)
			automation.GET("/rules/:rule_id/history", automationController.GetRuleExecutionHistory)

			//Templates (to be implemented)
			automation.GET("/templates", automationController.GetRuleTemplates)
			automation.POST("/templates", automationController.CreateRuleTemplate)
		}

		// Home-specific automation routes
		homeAutomation := r.Group("/api/homes/:home_id")
		homeAutomation.Use(authMiddleware.AuthMiddleware(), permMiddleware.RequireMember())
		{
			// Automation Rules for home
			homeAutomation.POST("/automation/rules", automationController.CreateHomeAutomationRule)
			homeAutomation.GET("/automation/rules", automationController.GetHomeAutomationRules)
			homeAutomation.GET("/automation/rules/:rule_id", automationController.GetHomeAutomationRule)
			homeAutomation.PUT("/automation/rules/:rule_id", automationController.UpdateHomeAutomationRule)
			homeAutomation.DELETE("/automation/rules/:rule_id", automationController.DeleteHomeAutomationRule)
			homeAutomation.PUT("/automation/rules/:rule_id/status", automationController.ToggleHomeAutomationRuleStatus)

			// Rule execution and history for home
			homeAutomation.POST("/automation/rules/:rule_id/execute", automationController.ExecuteHomeAutomationRule)
			homeAutomation.GET("/automation/executions", automationController.GetHomeAutomationExecutions)
		}

		// Device Types Management Routes (Read-only)
		deviceTypesAPI := r.Group("/api/device-types")
		deviceTypesAPI.Use(authMiddleware.AuthMiddleware())
		{
			// Read-only endpoints (any authenticated user)
			deviceTypesAPI.GET("", deviceTypeController.GetAllDeviceTypes)             // Lấy tất cả device types
			deviceTypesAPI.GET("/:device_type_id", deviceTypeController.GetDeviceType) // Chi tiết device type
		}

		return r
	}
}
