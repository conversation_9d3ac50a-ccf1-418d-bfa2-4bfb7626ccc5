import 'package:shared_preferences/shared_preferences.dart';
import '../../../core/base/base_view_model.dart';
import '../../../core/repositories/auth_repository.dart';
import '../../../core/models/user_model.dart';

/// ViewModel cho Auth module
/// Thay thế AuthBloc trong MVVM pattern
class AuthViewModel extends BaseViewModel {
  final AuthRepository _authRepository;

  AuthViewModel({required AuthRepository authRepository})
      : _authRepository = authRepository;

  // States
  User? _user;
  String? _token;
  bool _isAuthenticated = false;
  String? _tempEmail; // Email tạm thời cho OTP verification
  String? _pendingVerificationEmail; // Email đang chờ xác thực
  bool _isCheckingAuth = false;
  DateTime? _lastProfileLoad;
  static const Duration _profileCacheDuration =
      Duration(minutes: 5); // Cache profile for 5 minutes

  // Getters
  User? get user => _user;
  String? get token => _token;
  bool get isAuthenticated => _isAuthenticated;
  String? get tempEmail => _tempEmail;
  String? get pendingVerificationEmail => _pendingVerificationEmail;

  /// Kiểm tra trạng thái authentication khi khởi động app
  ///

  Future<void> checkAuthStatus({bool forceRefresh = false}) async {
    print(' [DEBUG] checkAuthStatus called - isCheckingAuth: $_isCheckingAuth, isLoading: $isLoading');

    if (_isCheckingAuth) {
      print(' checkAuthStatus already in progress, skipping...');
      return;
    }

    _isCheckingAuth = true;
    print(' [DEBUG] Starting auth check...');

    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');
      print(' [DEBUG] Token exists: ${token != null}');

      if (token != null) {
        try {
          // Check if we can use cached profile
          if (!forceRefresh && _user != null && _lastProfileLoad != null) {
            final now = DateTime.now();
            if (now.difference(_lastProfileLoad!) < _profileCacheDuration) {
              print(' [DEBUG] Using cached user profile');
              return; // Use cached data
            }
          }

          print(' [DEBUG] Loading fresh user profile');
          final user = await _authRepository.getProfileLegacy(token);
          _setAuthenticatedState(user, token);
          _lastProfileLoad = DateTime.now();
        } catch (e) {
          print(' [DEBUG] Profile load failed: $e');
          await prefs.remove('auth_token');
          _setUnauthenticatedState();
        }
      } else {
        print(' [DEBUG] No token found, setting unauthenticated');
        _setUnauthenticatedState();
      }
    } catch (e) {
      print(' [DEBUG] Auth check error: $e');
      _setUnauthenticatedState();
    } finally {
      _isCheckingAuth = false;
      print(' [DEBUG] Auth check completed - isLoading: $isLoading, isAuthenticated: $isAuthenticated');
    }
  }

  /// Đăng nhập
  Future<bool> login(String email, String password) async {
    final result = await handleAsync(() async {
      final apiResponse = await _authRepository.login(email, password);
final message = apiResponse.message ?? 'Đăng nhập thất bại';
print('DEBUG: message = "$message"');
if (message.toLowerCase().contains('chưa được xác thực')) {
  print('DEBUG: Set pendingVerificationEmail = $email');
  _pendingVerificationEmail = email;
  safeNotifyListeners();
  return false;
}
if (apiResponse.success) {
  // Xử lý đăng nhập thành công
  final data = apiResponse.data;
  final userEmail = data?['email'] ?? email;
  _tempEmail = userEmail;
  safeNotifyListeners();
  return true;
} else {
  setError(message);
  return false;
}
    }, errorPrefix: 'Lỗi đăng nhập');

    return result ?? false;
  }

  /// Đăng ký
  Future<bool> register({
    required String password,
    required String email,
  }) async {
    final result = await handleAsync(() async {
      final response = await _authRepository.register(
        password: password,
        email: email,
      );

      if (response.success) {
        // Lưu email để dùng cho email verification
        _pendingVerificationEmail = email;
        safeNotifyListeners();
        return true;
      } else {
        throw Exception(response.message ?? 'Đăng ký thất bại');
      }
    }, errorPrefix: 'Đăng ký thất bại');

    return result ?? false;
  }

  /// Xác thực email
  Future<bool> verifyEmail({
    required String email,
    required String otp,
  }) async {
    final result = await handleAsync(() async {
      final response = await _authRepository.verifyEmail(
        email: email,
        otp: otp,
      );

      if (response.success) {
        // Clear pending verification email
        _pendingVerificationEmail = null;
        safeNotifyListeners();
        return true;
      } else {
        throw Exception(response.message ?? 'Xác thực email thất bại');
      }
    }, errorPrefix: 'Xác thực email thất bại');

    return result ?? false;
  }

  /// Gửi lại mã xác thực email
  Future<bool> resendVerification(String email) async {
    final result = await handleAsync(() async {
      final response = await _authRepository.resendVerification(email);

      if (response.success) {
        return true;
      } else {
        throw Exception(response.message ?? 'Gửi lại mã xác thực thất bại');
      }
    }, errorPrefix: 'Gửi lại mã xác thực thất bại');

    return result ?? false;
  }

  /// Quên mật khẩu
  Future<bool> forgotPassword(String email) async {
    final result = await handleAsync(() async {
      final response = await _authRepository.forgotPassword(email);

      if (response.success) {
        return true;
      } else {
        throw Exception(response.message ?? 'Gửi email thất bại');
      }
    }, errorPrefix: 'Gửi email reset mật khẩu thất bại');

    return result ?? false;
  }

  /// Xác thực OTP
  /// Side effect: Tự động tạo home mặc định nếu user chưa có home
  Future<bool> verifyOTP(String email, String otp) async {
    final result = await handleAsync(() async {
      final response = await _authRepository.verifyOTPAndGetToken(email, otp);

      if (response['success'] == true || response['status'] == true) {
        final token = response['token'] ?? response['access_token'];

        if (token != null) {
          final prefs = await SharedPreferences.getInstance();
          await prefs.setString('access_token', token);
          await prefs.setString('auth_token', token);

          try {
            final user = await _authRepository.getProfileLegacy(token);
            _setAuthenticatedState(user, token);

            // Note: Backend automatically creates home if user doesn't have one
            // This is handled on the server side during OTP verification

            return true;
          } catch (profileError) {
            print(' Profile error after OTP verification: $profileError');
            // Still consider OTP verification successful, but user needs to complete profile
            _setPartialAuthState(token);
            return true;
          }
        }
      }

      throw Exception(response['message'] ?? 'Xác thực OTP thất bại');
    }, errorPrefix: 'Xác thực OTP thất bại');

    return result ?? false;
  }

  /// Gửi lại OTP
  Future<bool> resendOTP(String email) async {
    final result = await handleAsync(() async {
      await _authRepository.resendOTP(email);
      return true;
    }, errorPrefix: 'Gửi lại OTP thất bại');

    return result ?? false;
  }

  /// Reset mật khẩu
  Future<bool> resetPassword({
    required String email,
    required String otp,
    required String newPassword,
  }) async {
    final result = await handleAsync(() async {
      final response = await _authRepository.resetPassword(
        email: email,
        otp: otp,
        newPassword: newPassword,
      );

      if (response.success) {
        return true;
      } else {
        throw Exception(response.message ?? 'Reset mật khẩu thất bại');
      }
    }, errorPrefix: 'Reset mật khẩu thất bại');

    return result ?? false;
  }

  /// Đổi mật khẩu (cho người dùng đã đăng nhập)
  Future<bool> changePassword({
    required String oldPassword,
    required String newPassword,
  }) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('Không tìm thấy token xác thực');
      }

      final response = await _authRepository.changePassword(
        token: token,
        oldPassword: oldPassword,
        newPassword: newPassword,
      );

      if (response.success) {
        return true;
      } else {
        throw Exception(response.message ?? 'Đổi mật khẩu thất bại');
      }
    }, errorPrefix: 'Đổi mật khẩu thất bại');

    return result ?? false;
  }

  /// Cập nhật profile
  Future<bool> updateProfile({
    String? fullName,
    String? phoneNumber,
    String? avatarUrl,
  }) async {
    print(' DEBUG - AuthViewModel.updateProfile called');
    print(
        ' DEBUG - Parameters: fullName=$fullName, phoneNumber=$phoneNumber, avatarUrl=$avatarUrl');

    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      print(' DEBUG - Token exists: ${token != null}');

      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      print(' DEBUG - Calling repository.updateProfile...');

      try {
        final response = await _authRepository.updateProfile(
          token: token,
          fullName: fullName,
          phoneNumber: phoneNumber,
          avatarUrl: avatarUrl,
        );

        print(
            ' DEBUG - Repository response: success=${response.success}, message=${response.message}');

        if (response.success) {
          print(' DEBUG - Update successful');

          if (response.data != null) {
            // If we have user data, update the state
            print(' DEBUG - Updating user state with new data');
            _setAuthenticatedState(response.data!, token);
          } else {
            // If no user data returned, refresh user profile to get latest data
            print(' DEBUG - No user data returned, refreshing profile...');
            await checkAuthStatus(); // This will fetch latest user data
          }

          return true;
        } else {
          print(' DEBUG - Update failed: ${response.message}');
          throw Exception(response.message);
        }
      } catch (e) {
        // Handle special case where update was successful but no user data returned
        if (e.toString().contains('UPDATE_SUCCESS_NO_DATA')) {
          print(
              ' DEBUG - Update successful (no data returned), refreshing profile...');
          await checkAuthStatus(); // Refresh user data
          return true;
        }
        // Re-throw other exceptions
        rethrow;
      }
    }, errorPrefix: 'Cập nhật profile thất bại');

    print(' DEBUG - Final result: $result');
    return result ?? false;
  }

  /// Đăng xuất
  Future<void> logout() async {
    print('LOGOUT START');
    final prefs = await SharedPreferences.getInstance();
    await prefs.clear();

    _user = null;
    _token = null;
    _isAuthenticated = false;
    _tempEmail = null;
    _isCheckingAuth = false;

    print('LOGOUT - isAuthenticated: $_isAuthenticated');
    notifyListeners();
    print('LOGOUT DONE');
  }

  /// Set authenticated state
  void _setAuthenticatedState(User user, String token) {
    _user = user;
    _token = token;
    _isAuthenticated = true;
    _tempEmail = null;
    safeNotifyListeners();
  }

  void _setUnauthenticatedState() {
    print(' [DEBUG] Set unauthenticated state');
    _user = null;
    _token = null;
    _isAuthenticated = false;
    _tempEmail = null;
    print(' [DEBUG] About to notify listeners');
    safeNotifyListeners();
    print(' [DEBUG] Listeners notified');
  }

  /// Set partial auth state (token valid but profile incomplete)
  void _setPartialAuthState(String token) {
    _isAuthenticated = true;
    _user = null; // Profile will be loaded later
    _token = token;
    safeNotifyListeners();
  }

  /// Clear temp email
  void clearTempEmail() {
    _tempEmail = null;
    safeNotifyListeners();
  }
}
