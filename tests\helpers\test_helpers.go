package helpers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"time"

	"dh52110724-api-quan-ly-nha-thong-minh/models"
	"dh52110724-api-quan-ly-nha-thong-minh/utils"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// TestUser struct cho test data
type TestUser struct {
	ID       int
	Email    string
	Password string
	FullName string
	Token    string
}

// TestHome struct cho test data
type TestHome struct {
	ID      int
	Name    string
	Address string
	OwnerID int
}

// TestDevice struct cho test data
type TestDevice struct {
	ID               int
	Name             string
	DeviceTypeID     int
	HomeID           int
	AreaID           *int
	UniqueIdentifier string
	IsOnline         bool
}

// CreateTestUser tạo user test
func CreateTestUser(db *gorm.DB, email, password, fullName string) (*TestUser, error) {
	hashedPassword, err := utils.HashPassword(password)
	if err != nil {
		return nil, err
	}

	user := &models.User{
		Email:        email,
		PasswordHash: hashedPassword,
		FullName:     fullName,
		IsVerified:   true,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	if err := db.Create(user).Error; err != nil {
		return nil, err
	}

	// Generate JWT token for testing
	token, err := utils.GenerateJWT(user.Email, user.UserID)
	if err != nil {
		return nil, err
	}

	return &TestUser{
		ID:       user.UserID,
		Email:    user.Email,
		Password: password,
		FullName: user.FullName,
		Token:    token,
	}, nil
}

// CreateTestHome tạo home test
func CreateTestHome(db *gorm.DB, name, address string, ownerID int) (*TestHome, error) {
	home := &models.Home{
		Name:      name,
		Address:   address,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	if err := db.Create(home).Error; err != nil {
		return nil, err
	}

	// Tạo home_user relationship với role OWNER (role_id = 1)
	homeUser := &models.HomeUser{
		HomeID:    home.HomeID,
		UserID:    ownerID,
		RoleID:    1, // OWNER
		CreatedAt: time.Now(),
	}

	if err := db.Create(homeUser).Error; err != nil {
		return nil, err
	}

	return &TestHome{
		ID:      home.HomeID,
		Name:    home.Name,
		Address: home.Address,
		OwnerID: ownerID,
	}, nil
}

// CreateTestArea tạo area test
func CreateTestArea(db *gorm.DB, name string, homeID int) (*models.Area, error) {
	area := &models.Area{
		Name:      name,
		HomeID:    homeID,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	if err := db.Create(area).Error; err != nil {
		return nil, err
	}

	return area, nil
}

// CreateTestDevice tạo device test
func CreateTestDevice(db *gorm.DB, name string, deviceTypeID, homeID int, areaID *int) (*TestDevice, error) {
	device := &models.Device{
		Name:             name,
		DeviceTypeID:     &deviceTypeID,
		HomeID:           &homeID,
		AreaID:           areaID,
		ConnectionTypeID: 1, // WiFi
		UniqueIdentifier: fmt.Sprintf("test_device_%d_%s", time.Now().Unix(), name),
		IsOnline:         true,
		Status:           "active",
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	if err := db.Create(device).Error; err != nil {
		return nil, err
	}

	return &TestDevice{
		ID:               device.DeviceID,
		Name:             device.Name,
		DeviceTypeID:     *device.DeviceTypeID,
		HomeID:           *device.HomeID,
		AreaID:           device.AreaID,
		UniqueIdentifier: device.UniqueIdentifier,
		IsOnline:         device.IsOnline,
	}, nil
}

// CreateTestPermission tạo permission test
func CreateTestPermission(db *gorm.DB, userID int, deviceID *int, areaID *int, canView, canControl, canConfigure bool) error {
	permission := &models.Permission{
		UserID:       userID,
		DeviceID:     deviceID,
		AreaID:       areaID,
		CanView:      canView,
		CanControl:   canControl,
		CanConfigure: canConfigure,
	}

	return db.Create(permission).Error
}

// MakeRequest helper để tạo HTTP request cho testing
func MakeRequest(method, url string, body interface{}, token string) (*httptest.ResponseRecorder, error) {
	var reqBody []byte
	var err error

	if body != nil {
		reqBody, err = json.Marshal(body)
		if err != nil {
			return nil, err
		}
	}

	req, err := http.NewRequest(method, url, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")
	if token != "" {
		req.Header.Set("Authorization", "Bearer "+token)
	}

	w := httptest.NewRecorder()
	return w, nil
}

// ParseResponse helper để parse JSON response
func ParseResponse(w *httptest.ResponseRecorder, result interface{}) error {
	return json.Unmarshal(w.Body.Bytes(), result)
}

// AssertStatusCode kiểm tra status code
func AssertStatusCode(w *httptest.ResponseRecorder, expectedCode int) bool {
	return w.Code == expectedCode
}

// SetupGinTestMode setup Gin cho testing
func SetupGinTestMode() {
	gin.SetMode(gin.TestMode)
}

// CleanupTestData xóa test data
func CleanupTestData(db *gorm.DB) {
	// Xóa theo thứ tự để tránh foreign key constraint
	db.Exec("DELETE FROM automation_executions")
	db.Exec("DELETE FROM automation_rules")
	db.Exec("DELETE FROM device_commands")
	db.Exec("DELETE FROM permissions")
	db.Exec("DELETE FROM devices")
	db.Exec("DELETE FROM areas")
	db.Exec("DELETE FROM home_users")
	db.Exec("DELETE FROM homes")
	db.Exec("DELETE FROM invitations")
	db.Exec("DELETE FROM users")
}
